import 'dotenv/config';
import express from 'express';
import axios from 'axios';

const app = express();
const port = 7860;

// The endpoint of the main Gemini CLI Wrapper
const GEMINI_WRAPPER_ENDPOINT = 'http://localhost:8010/v1/chat/completions';

app.use(express.json());

app.post('/search', async (req, res) => {
  const { query } = req.body;

  if (!query) {
    return res.status(400).json({ error: 'Query is required' });
  }

  console.log(`[MCP Search Engine] Received query: "${query}"`);

  // This prompt instructs the LLM to act as a search engine and provide a direct answer.
  const prompt = `You are an AI assistant acting as a highly-detailed search engine. For the given query, provide a multi-paragraph comprehensive overview. After the overview, include a section with the heading "Key Details:" followed by a bulleted list of the most important facts, figures, and specific details. Do not add any conversational fluff. Query: "${query}"`;

  const payload = {
    model: 'gemini-2.5-flash', // Using the model specified by the user
    messages: [{ role: 'user', content: prompt }],
    stream: false,
    temperature: 0.2, // Lower temperature for more factual, less creative responses
  };

  try {
    const startTime = Date.now();
    console.log(`[MCP Search Engine] Sending request to Gemini Wrapper at ${GEMINI_WRAPPER_ENDPOINT}`);
    const headers = { 'Content-Type': 'application/json' };
    // Attach Authorization header if API_KEY is available
    const apiKey = process.env.API_KEY || process.env.USER_API_KEY || process.env.ADMIN_API_KEY || process.env.GUEST_API_KEY;
    if (apiKey) {
      headers['Authorization'] = `Bearer ${apiKey}`;
      console.log('[MCP Search Engine] Using Authorization header from environment');
    } else {
      console.warn('[MCP Search Engine] No API key found in environment; request may be rejected by wrapper');
    }

    const wrapperResponse = await axios.post(GEMINI_WRAPPER_ENDPOINT, payload, { headers });
    const endTime = Date.now();
    const responseTimeInMs = endTime - startTime;

    // Extract the content from the response (following OpenAI's structure)
    const summary = wrapperResponse.data?.choices?.[0]?.message?.content;

    if (!summary) {
      throw new Error('No content returned from the model.');
    }

    console.log(`[MCP Search Engine] Successfully received summary from wrapper in ${responseTimeInMs}ms.`);

    // Return the structured JSON response
    res.json({
      query: query,
      status: 'success',
      responseTimeInMs: responseTimeInMs,
      results: [
        {
          source: 'Gemini-CLI-Wrapper (gemini-2.5-flash)',
          relevance_score: 0.95, // Static score, can be improved with more logic
          summary: summary.trim(),
        },
      ],
    });

  } catch (error) {
    console.error('[MCP Search Engine] Error calling Gemini Wrapper:', error.response ? JSON.stringify(error.response.data, null, 2) : error.message);
    res.status(500).json({
      query: query,
      status: 'error',
      message: 'Failed to communicate with the Gemini Wrapper API.',
      error_details: error.response ? error.response.data : { message: error.message },
    });
  }
});

app.listen(port, () => {
  console.log(`🚀 MCP Search Engine is running on http://localhost:${port}`);
  console.log('Ready to receive search queries at /search');
});